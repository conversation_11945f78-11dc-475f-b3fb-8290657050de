#!/usr/bin/env python3
"""
Valorant Live Detection Terminal Application

A dedicated terminal-based tool for real-time Valorant match detection and teammate analysis.
Run this in a separate terminal window for continuous monitoring.
"""

import sys
import os
import time
import threading
import logging
from datetime import datetime
from typing import Dict, Any, List

# Suppress debug logs for cleaner output
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('src.valorant_client').setLevel(logging.WARNING)

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.valorant_client import is_valorant_running
from src.api_client import <PERSON>orantAP<PERSON>
from src.live_match_detector import LiveMatchDetector

class TerminalLiveDetector:
    """Terminal-based live detection application."""
    
    def __init__(self):
        self.api = ValorantAPI()
        self.detector = LiveMatchDetector(self.api)
        self.current_data = None
        self.running = False
        self.last_update = None
        
    def clear_screen(self):
        """Clear the terminal screen."""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """Print the application header."""
        print("=" * 80)
        print("🎯 VALORANT LIVE DETECTION TERMINAL")
        print("=" * 80)
        print(f"⏰ Last Update: {datetime.now().strftime('%H:%M:%S')}")
        print(f"🔄 Status: {'🟢 MONITORING' if self.running else '🔴 STOPPED'}")
        print("-" * 80)
    
    def print_match_info(self, match_data: Dict[str, Any]):
        """Print current match information."""
        state = match_data.get('state', 'Unknown')
        mode = match_data.get('mode', 'Unknown')
        map_name = match_data.get('map', 'Unknown')
        message = match_data.get('message', '')
        
        # State indicator
        state_emoji = {
            'MENUS': '🏠',
            'PREGAME': '⚡',
            'INGAME': '🎮',
            'SWIFTPLAY': '⚡',
            'DEATHMATCH': '💀',
            'COMPETITIVE': '🏆',
            'UNRATED': '🎯',
            'CONNECTED': '🔗',
            'OTHER_MODE': '❓'
        }.get(state, '❓')
        
        print(f"{state_emoji} GAME STATE: {state}")
        print(f"🎮 MODE: {mode}")
        print(f"🗺️  MAP: {map_name}")
        
        if message:
            print(f"💬 INFO: {message}")
        
        print("-" * 80)
    
    def print_players(self, players: List[Dict[str, Any]]):
        """Print player information in a formatted table."""
        if not players:
            print("👥 PLAYERS: No player data available")
            print("-" * 80)
            return
        
        print(f"👥 PLAYERS ({len(players)}):")
        print()
        
        # Header
        print(f"{'#':<3} {'NAME':<20} {'RANK':<25} {'TEAM':<12} {'AGENT':<12}")
        print("-" * 80)
        
        # Player rows
        for i, player in enumerate(players, 1):
            name = player.get('name', 'Unknown')
            tag = player.get('tag', '')
            if tag and tag != 'Unknown':
                display_name = f"{name}#{tag}"
            else:
                display_name = name
            
            rank = player.get('rank', 'Unknown')
            rr = player.get('rr', 0)
            if rr > 0 and rank != 'Unknown':
                rank_display = f"{rank} ({rr} RR)"
            else:
                rank_display = rank
            
            team = player.get('team', 'Unknown')
            agent = player.get('agent', 'Unknown')
            
            # Truncate long names
            if len(display_name) > 19:
                display_name = display_name[:16] + "..."
            if len(rank_display) > 24:
                rank_display = rank_display[:21] + "..."
            
            print(f"{i:<3} {display_name:<20} {rank_display:<25} {team:<12} {agent:<12}")
        
        print("-" * 80)
    
    def print_instructions(self):
        """Print usage instructions."""
        print("📋 INSTRUCTIONS:")
        print("• This tool monitors your Valorant matches in real-time")
        print("• For full teammate data: Queue Competitive or Unrated")
        print("• Swiftplay/Deathmatch have limited data (Valorant API restriction)")
        print("• Press Ctrl+C to stop monitoring")
        print("-" * 80)
    
    def print_status_info(self):
        """Print current status information."""
        if not is_valorant_running():
            print("❌ VALORANT NOT RUNNING")
            print("   Please start Valorant to begin monitoring")
        elif not self.current_data:
            print("🔍 SEARCHING FOR MATCH DATA...")
            print("   Waiting for game state detection...")
        else:
            print("✅ MONITORING ACTIVE")
        
        print("-" * 80)
    
    def match_callback(self, match_data: Dict[str, Any]):
        """Callback function for match data updates."""
        self.current_data = match_data
        self.last_update = datetime.now()
        self.update_display()
    
    def update_display(self):
        """Update the terminal display with current information."""
        self.clear_screen()
        self.print_header()
        
        if not is_valorant_running():
            self.print_status_info()
            self.print_instructions()
            return
        
        if self.current_data:
            self.print_match_info(self.current_data)
            
            players = self.current_data.get('players', [])
            self.print_players(players)
            
            # Additional info
            note = self.current_data.get('note')
            if note:
                print(f"📝 NOTE: {note}")
                print("-" * 80)
        else:
            self.print_status_info()
        
        self.print_instructions()
    
    def start_monitoring(self):
        """Start the live detection monitoring."""
        print("🚀 Starting Valorant Live Detection...")
        
        if not is_valorant_running():
            print("❌ Valorant is not running!")
            print("Please start Valorant and try again.")
            return False
        
        # Add callback
        self.detector.add_callback(self.match_callback)
        
        # Start monitoring
        if self.detector.start_monitoring():
            self.running = True
            print("✅ Monitoring started successfully!")
            time.sleep(1)  # Brief pause before clearing screen
            return True
        else:
            print("❌ Failed to start monitoring!")
            print("Make sure Valorant is running and try again.")
            return False
    
    def stop_monitoring(self):
        """Stop the live detection monitoring."""
        self.running = False
        self.detector.stop_monitoring()
        print("\n🛑 Monitoring stopped.")
    
    def run(self):
        """Main application loop."""
        try:
            # Initial display
            self.update_display()
            
            # Start monitoring
            if not self.start_monitoring():
                input("\nPress Enter to exit...")
                return
            
            # Main loop - update display periodically
            while self.running:
                try:
                    # Check if Valorant is still running
                    if not is_valorant_running():
                        self.current_data = None
                        self.running = False
                        self.detector.stop_monitoring()
                        self.update_display()
                        print("\n❌ Valorant closed. Monitoring stopped.")
                        break
                    
                    # Update display every 2 seconds even without new data
                    if not self.current_data or (
                        self.last_update and 
                        (datetime.now() - self.last_update).seconds > 5
                    ):
                        self.update_display()
                    
                    time.sleep(2)
                    
                except KeyboardInterrupt:
                    break
                    
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_monitoring()
            print("\nThanks for using Valorant Live Detection! 🎯")

def main():
    """Main entry point."""
    try:
        app = TerminalLiveDetector()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
