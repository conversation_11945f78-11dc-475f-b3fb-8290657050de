#!/usr/bin/env python3
"""
Test the improved live detection system.
"""

import sys
import os
import time

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.valorant_client import ValorantLocalClient, is_valorant_running
from src.api_client import ValorantAPI
from src.live_match_detector import LiveMatchDetector

def test_improved_detection():
    """Test the improved live detection system."""
    print("🎯 Testing Improved Live Detection System")
    print("=" * 60)
    
    if not is_valorant_running():
        print("❌ Valorant is not running!")
        return
    
    print("✅ Valorant is running")
    
    try:
        # Initialize the system
        api = ValorantAPI()
        detector = LiveMatchDetector(api)
        
        print("✅ Live match detector initialized")
        
        # Add a callback to see what data we get
        def match_callback(match_data):
            print(f"\n📊 Live Detection Result:")
            print(f"   State: {match_data.get('state', 'Unknown')}")
            print(f"   Mode: {match_data.get('mode', 'Unknown')}")
            print(f"   Map: {match_data.get('map', 'Unknown')}")
            print(f"   Message: {match_data.get('message', 'No message')}")
            
            players = match_data.get('players', [])
            print(f"   Players: {len(players)}")
            
            if players:
                print("   Player details:")
                for i, player in enumerate(players):
                    name = player.get('name', 'Unknown')
                    rank = player.get('rank', 'Unknown')
                    team = player.get('team', 'Unknown')
                    print(f"     {i+1}. {name} - {rank} ({team})")
            
            if match_data.get('note'):
                print(f"   Note: {match_data.get('note')}")
        
        detector.add_callback(match_callback)
        
        # Start monitoring
        print("\n🔄 Starting improved live detection...")
        if detector.start_monitoring():
            print("✅ Monitoring started successfully!")
            print("\n⏳ Monitoring for 15 seconds...")
            print("   The system should now properly detect Swiftplay and other modes!")
            
            # Monitor for 15 seconds
            for i in range(15):
                time.sleep(1)
                if i % 3 == 0:
                    print(f"   Monitoring... {15-i}s remaining")
            
            detector.stop_monitoring()
            print("\n✅ Monitoring completed")
            return True
        else:
            print("❌ Failed to start monitoring")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function."""
    print("🎯 Improved Live Detection Test")
    print("This tests the enhanced system that properly handles Swiftplay and other modes")
    print()
    
    success = test_improved_detection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test completed!")
        print("\n💡 What you should see:")
        print("   ✅ Swiftplay mode properly detected")
        print("   ✅ Map name shown (e.g., 'Port')")
        print("   ✅ Your player info with rank")
        print("   ✅ Clear message about API limitations")
        print("\n🎯 For full teammate data:")
        print("   1. Queue for Competitive or Unrated")
        print("   2. Wait for agent select screen")
        print("   3. Run the detection again")
    else:
        print("❌ Test failed - check Valorant connection")

if __name__ == "__main__":
    main()
