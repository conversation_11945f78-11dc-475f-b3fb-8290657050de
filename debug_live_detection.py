#!/usr/bin/env python3
"""
Debug script for Valorant live match detection system.
This will help identify why the live detection can't grab player information and ranks.
"""

import sys
import os
import time

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.valorant_client import ValorantLocalClient, is_valorant_running
from src.api_client import ValorantAPI
from src.live_match_detector import LiveMatchDetector

def test_valorant_connection():
    """Test connection to local Valorant client."""
    print("🎮 Testing Valorant Local Client Connection...")
    print("=" * 60)
    
    # Check if Valorant is running
    if not is_valorant_running():
        print("❌ Valorant is not running!")
        print("\n💡 To fix this:")
        print("   1. Start Valorant")
        print("   2. Wait for it to fully load (past main menu)")
        print("   3. Try entering Practice Range or a match")
        print("   4. Run this script again")
        return False
    
    print("✅ Valorant is running")
    
    # Test connection
    client = ValorantLocalClient()
    print("\n🔌 Attempting to connect to Valorant client...")
    
    if client.connect():
        print("✅ Successfully connected to Valorant client!")
        
        # Test basic endpoints
        print("\n🧪 Testing basic endpoints...")
        
        # Test player UUID
        player_uuid = client._get_player_uuid()
        if player_uuid:
            print(f"✅ Player UUID: {player_uuid}")
        else:
            print("❌ Could not get player UUID")
        
        # Test game state
        game_state = client.get_current_game_state()
        print(f"🎯 Current game state: {game_state}")
        
        # Test detailed game state
        detailed_state = client.get_detailed_game_state()
        print(f"📊 Detailed state: {detailed_state}")
        
        return True
    else:
        print("❌ Failed to connect to Valorant client!")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Restart Valorant completely")
        print("   2. Run both Valorant and this script as Administrator")
        print("   3. Make sure no other Valorant tools are running")
        print("   4. Try entering a Practice Range or match")
        return False

def test_match_detection():
    """Test live match detection functionality."""
    print("\n🔍 Testing Live Match Detection...")
    print("=" * 60)
    
    client = ValorantLocalClient()
    if not client.connect():
        print("❌ Cannot test match detection - client connection failed")
        return False
    
    print("✅ Client connected, testing match detection...")
    
    # Test current match
    print("\n🎯 Testing current match detection...")
    current_match = client.get_current_match()
    if current_match:
        print("✅ Current match data found!")
        print(f"   Match ID: {current_match.get('MatchID', 'Unknown')}")
        print(f"   Map: {current_match.get('MapID', 'Unknown')}")
        print(f"   Mode: {current_match.get('Mode', 'Unknown')}")
        print(f"   Players: {len(current_match.get('Players', []))}")
        return current_match
    else:
        print("⚠️ No current match data found")
    
    # Test pregame match
    print("\n🎯 Testing pregame match detection...")
    pregame_match = client.get_pregame_match()
    if pregame_match:
        print("✅ Pregame match data found!")
        print(f"   Match ID: {pregame_match.get('MatchID', 'Unknown')}")
        print(f"   Map: {pregame_match.get('MapID', 'Unknown')}")
        print(f"   State: {pregame_match.get('State', 'Unknown')}")
        print(f"   Players: {len(pregame_match.get('AllyTeam', {}).get('Players', []))}")
        return pregame_match
    else:
        print("⚠️ No pregame match data found")
    
    # Test party info
    print("\n👥 Testing party information...")
    party_info = client.get_party_info()
    if party_info:
        print("✅ Party data found!")
        members = party_info.get('Members', [])
        print(f"   Party members: {len(members)}")
        for i, member in enumerate(members[:3]):  # Show first 3 members
            print(f"   Member {i+1}: {member.get('Subject', 'Unknown')}")
        return party_info
    else:
        print("⚠️ No party data found")
    
    # Test presence info
    print("\n👤 Testing presence information...")
    presence_info = client.get_presence()
    if presence_info:
        print("✅ Presence data found!")
        presences = presence_info.get('presences', [])
        print(f"   Presences: {len(presences)}")
        return presence_info
    else:
        print("⚠️ No presence data found")
    
    print("\n❌ No match data found through any method")
    print("\n💡 This could mean:")
    print("   1. You're not in a match or queue")
    print("   2. You're in a mode that doesn't provide player data (like Deathmatch)")
    print("   3. The Valorant API is not exposing match data")
    print("   4. Try entering a Competitive or Unrated match")
    
    return None

def test_player_name_resolution():
    """Test player name resolution from PUUIDs."""
    print("\n👤 Testing Player Name Resolution...")
    print("=" * 60)
    
    client = ValorantLocalClient()
    if not client.connect():
        print("❌ Cannot test name resolution - client connection failed")
        return False
    
    # Get your own PUUID first
    player_uuid = client._get_player_uuid()
    if not player_uuid:
        print("❌ Could not get your player UUID")
        return False
    
    print(f"✅ Your PUUID: {player_uuid}")
    
    # Test name resolution
    print("\n🔍 Testing name resolution...")
    name_dict = client.get_player_names_from_puuids([player_uuid])
    
    if name_dict and player_uuid in name_dict:
        print(f"✅ Name resolution works!")
        print(f"   Your name: {name_dict[player_uuid]}")
        return True
    else:
        print("❌ Name resolution failed")
        print("   This might affect teammate name display")
        return False

def test_rank_integration():
    """Test integration with HenrikDev API for rank fetching."""
    print("\n🏆 Testing Rank Integration...")
    print("=" * 60)
    
    try:
        api = ValorantAPI()
        print("✅ HenrikDev API client initialized")
        
        # Test with your own account
        print("\n🔍 Testing rank lookup for your account...")
        player = api.get_player_full_info("rileyx", "umi")
        
        if player:
            print("✅ Rank lookup works!")
            print(f"   Your rank: {player.display_rank}")
            print(f"   Peak rank: {player.display_peak}")
            return True
        else:
            print("❌ Could not fetch your rank data")
            return False
            
    except Exception as e:
        print(f"❌ Rank integration error: {e}")
        return False

def test_full_live_detection():
    """Test the complete live detection system."""
    print("\n🎯 Testing Complete Live Detection System...")
    print("=" * 60)
    
    try:
        # Initialize the live detector
        api = ValorantAPI()
        detector = LiveMatchDetector(api)
        
        print("✅ Live match detector initialized")
        
        # Add a callback to see what data we get
        def match_callback(match_data):
            print(f"\n📊 Match data received:")
            print(f"   State: {match_data.get('state', 'Unknown')}")
            print(f"   Mode: {match_data.get('mode', 'Unknown')}")
            print(f"   Map: {match_data.get('map', 'Unknown')}")
            print(f"   Players: {len(match_data.get('players', []))}")
            
            if match_data.get('players'):
                print("   Player details:")
                for i, player in enumerate(match_data['players'][:3]):  # Show first 3
                    name = player.get('name', 'Unknown')
                    rank = player.get('rank', 'Unknown')
                    print(f"     {i+1}. {name} - {rank}")
        
        detector.add_callback(match_callback)
        
        # Start monitoring
        print("\n🔄 Starting live detection monitoring...")
        if detector.start_monitoring():
            print("✅ Monitoring started successfully!")
            print("\n⏳ Monitoring for 30 seconds...")
            print("   (Try entering a match or queue during this time)")
            
            # Monitor for 30 seconds
            for i in range(30):
                time.sleep(1)
                if i % 5 == 0:
                    print(f"   Monitoring... {30-i}s remaining")
            
            detector.stop_monitoring()
            print("\n✅ Monitoring completed")
            return True
        else:
            print("❌ Failed to start monitoring")
            return False
            
    except Exception as e:
        print(f"❌ Live detection error: {e}")
        return False

def main():
    """Main debug function."""
    print("🎯 Valorant Live Detection Debug Tool")
    print("=" * 60)
    print("This tool will help identify why live detection can't grab player info and ranks")
    print()
    
    # Test 1: Valorant connection
    if not test_valorant_connection():
        print("\n❌ Cannot proceed - Valorant connection failed")
        return
    
    # Test 2: Match detection
    match_data = test_match_detection()
    
    # Test 3: Name resolution
    test_player_name_resolution()
    
    # Test 4: Rank integration
    test_rank_integration()
    
    # Test 5: Full live detection (only if Valorant is connected)
    print("\n" + "=" * 60)
    choice = input("Do you want to test live detection monitoring? (y/n): ").lower()
    if choice == 'y':
        test_full_live_detection()
    
    print("\n" + "=" * 60)
    print("🎉 Debug tests completed!")
    print("\n💡 Summary:")
    print("   - If Valorant connection failed: Restart Valorant and run as admin")
    print("   - If no match data found: Enter a Competitive/Unrated match")
    print("   - If name resolution failed: Check Valorant API permissions")
    print("   - If rank integration failed: Check your .env file and API key")

if __name__ == "__main__":
    main()
