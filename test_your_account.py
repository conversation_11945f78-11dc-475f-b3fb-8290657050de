#!/usr/bin/env python3
"""
Test the HenrikDev API with your own Riot ID.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_with_your_account():
    """Test with your own Riot ID."""
    api_key = os.getenv('HENRIKDEV_API_KEY')
    
    if not api_key:
        print("❌ No API key found!")
        return
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    # Get user's Riot ID
    print("\n🎯 Let's test with YOUR Riot ID!")
    print("Enter your Valorant Riot ID (the name and tag you use to log in)")
    print("Example: If your Riot ID is 'PlayerName#1234', enter:")
    print("  Name: PlayerName")
    print("  Tag: 1234")
    
    name = input("\nEnter your Riot ID name: ").strip()
    tag = input("Enter your Riot ID tag (without #): ").strip()
    
    if not name or not tag:
        print("❌ Please provide both name and tag!")
        return
    
    print(f"\n🔍 Testing with: {name}#{tag}")
    
    base_url = "https://api.henrikdev.xyz"
    headers = {
        'Authorization': api_key,
        'Accept': 'application/json',
        'User-Agent': 'ValorantTool/1.0'
    }
    
    # Test account endpoint (v1 first, then v2)
    for version in ['v1', 'v2']:
        print(f"\n📡 Testing {version} account endpoint...")
        url = f"{base_url}/valorant/{version}/account/{name}/{tag}"
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Account found:")
                account_data = data.get('data', {})
                print(f"   PUUID: {account_data.get('puuid', 'N/A')}")
                print(f"   Region: {account_data.get('region', 'N/A')}")
                print(f"   Level: {account_data.get('account_level', 'N/A')}")
                print(f"   Name: {account_data.get('name', 'N/A')}")
                print(f"   Tag: {account_data.get('tag', 'N/A')}")
                
                # Test MMR endpoint
                print(f"\n🏆 Testing MMR endpoint...")
                mmr_url = f"{base_url}/valorant/v3/mmr/na/pc/{name}/{tag}"
                print(f"MMR URL: {mmr_url}")
                
                mmr_response = requests.get(mmr_url, headers=headers, timeout=10)
                print(f"📊 MMR Status Code: {mmr_response.status_code}")
                
                if mmr_response.status_code == 200:
                    mmr_data = mmr_response.json()
                    mmr_info = mmr_data.get('data', {})
                    current_tier = mmr_info.get('currenttierpatched', 'Unknown')
                    rr = mmr_info.get('ranking_in_tier', 0)
                    elo = mmr_info.get('elo', 0)
                    print(f"✅ Current Rank: {current_tier}")
                    print(f"✅ RR: {rr}")
                    print(f"✅ ELO: {elo}")
                    
                    # Check peak rank
                    peak_tier = mmr_info.get('highest_rank', {})
                    if peak_tier:
                        peak_name = peak_tier.get('patched_tier', 'Unknown')
                        peak_season = peak_tier.get('season', 'Unknown')
                        print(f"✅ Peak Rank: {peak_name} (Season {peak_season})")
                    
                elif mmr_response.status_code == 404:
                    print(f"⚠️ No competitive data found (player might be unranked)")
                else:
                    print(f"❌ MMR request failed: {mmr_response.text[:200]}")
                
                return True  # Success, stop testing
                
            elif response.status_code == 404:
                print(f"❌ Account not found with {version}")
                if version == 'v2':
                    print("\n💡 Possible reasons:")
                    print("   1. Riot ID is incorrect (check spelling and tag)")
                    print("   2. Account doesn't exist in Valorant")
                    print("   3. Account is very new or inactive")
            elif response.status_code == 401:
                print(f"❌ Unauthorized - API key might be invalid")
                return False
            elif response.status_code == 429:
                print(f"❌ Rate limited - too many requests")
                return False
            else:
                print(f"❌ Error {response.status_code}: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return False
    
    print("\n❌ Could not find your account with either API version")
    print("\n🔧 Troubleshooting steps:")
    print("1. Double-check your Riot ID spelling")
    print("2. Make sure you're using your Valorant account (not just Riot account)")
    print("3. Try a different region if you're not in NA")
    print("4. Check if your account has played Valorant recently")
    
    return False

if __name__ == "__main__":
    test_with_your_account()
