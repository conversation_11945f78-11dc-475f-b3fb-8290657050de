# Valorant Tools System Overview

Your Valorant tools project has **two main components** that work together:

## 🎯 1. HenrikDev API System (Rank Fetching)
**Files**: `src/api_client.py`, `main.py`, `app.py`
**Purpose**: Fetch player ranks, stats, and match history from external servers

### What it does:
- ✅ **Player rank lookup** (current rank, RR, peak rank)
- ✅ **Account information** (level, region, platform)
- ✅ **Match history** (recent games, stats)
- ✅ **Batch player lookup** (multiple players at once)
- ✅ **Web interface** (Flask app)

### Status: **WORKING PERFECTLY** ✅
- Successfully fetches your rank: Immortal 1 (20 RR)
- API key is valid and configured correctly
- All endpoints responding properly

## 🎮 2. Local Valorant Client API (Live Match Detection)
**Files**: `src/valorant_client.py`, `src/live_match_detector.py`
**Purpose**: Connect to your local Valorant game client to detect live matches

### What it does:
- 🔍 **Live match detection** (detect when you're in a game)
- 👥 **Teammate information** (who's in your current match)
- 🎯 **Real-time game state** (pregame, in-game, menus)
- 📊 **Live match data** (map, game mode, players)

### Status: **Requires Valorant to be running** ⚠️
- Only works when Valorant game client is open
- Needs to connect to local Valorant API
- Used for live match analysis and teammate checking

## 🌐 3. Web Interface
**Files**: `app.py`, `templates/`
**Purpose**: Web-based interface for both systems

### Features:
- 🖥️ **Web-based rank checker**
- 📱 **Mobile-friendly interface**
- 🔴 **Live match detection** (when Valorant is running)
- 📊 **Match history display**
- 🏆 **Peak RR display**

## How to Use Each System

### For Rank Checking (Always Available):
```bash
# Command line interface
python main.py

# Web interface
python app.py
# Then visit: http://localhost:5000
```

### For Live Match Detection (Requires Valorant Running):
```bash
# Start Valorant first, then:
python app.py
# Visit web interface and use live match features
```

## Troubleshooting

### If rank fetching doesn't work:
1. Check your `.env` file has valid `HENRIKDEV_API_KEY`
2. Verify internet connection
3. Make sure player names/tags are correct
4. Some players might be unranked (no competitive data)

### If live match detection doesn't work:
1. **Start Valorant first** (must be running)
2. **Wait for Valorant to fully load** (past main menu)
3. **Try entering Practice Range or a match**
4. **Run as Administrator** if needed
5. **Close other Valorant tools** that might conflict

## Your Current Status: ✅ WORKING
- ✅ HenrikDev API: Working perfectly
- ✅ Rank fetching: Successfully showing Immortal 1
- ✅ Web interface: Available
- ⚠️ Live match detection: Only works when Valorant is running
