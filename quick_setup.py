#!/usr/bin/env python3
"""
Quick account setup - automatically configure rileyx#umi for testing.
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.account_config import account_config
from src.api_client import ValorantAP<PERSON>

def quick_setup():
    """Quick setup with your known account."""
    print("🎯 Quick Account Setup")
    print("=" * 40)
    
    # Set your account
    name = "rileyx"
    tag = "umi"
    
    print(f"Setting up account: {name}#{tag}")
    
    # Test the account first
    try:
        api = ValorantAPI()
        player_info = api.get_player_full_info(name, tag)
        
        if player_info:
            print("✅ Account verified!")
            print(f"   Level: {player_info.account_level}")
            print(f"   Rank: {player_info.display_rank}")
            
            # Save the configuration
            account_config.set_account(name, tag)
            print(f"✅ Account configured successfully!")
            return True
        else:
            print("❌ Account not found!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    if quick_setup():
        print("\n🎉 Setup complete! You can now run:")
        print("   python live_detection_terminal.py")
    else:
        print("\n❌ Setup failed!")
