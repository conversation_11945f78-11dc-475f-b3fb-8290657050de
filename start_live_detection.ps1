# Valorant Live Detection Terminal Launcher
$Host.UI.RawUI.WindowTitle = "Valorant Live Detection Terminal"

Write-Host "🎯 Starting Valorant Live Detection..." -ForegroundColor Cyan
Write-Host ""

# Change to script directory
Set-Location $PSScriptRoot

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found! Please install Python." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Run the live detection
try {
    python live_detection_terminal.py
} catch {
    Write-Host "❌ Error running live detection: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
