#!/usr/bin/env python3
"""
Direct API test to check HenrikDev API functionality.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_api_directly():
    """Test the HenrikDev API directly."""
    api_key = os.getenv('HENRIKDEV_API_KEY')
    
    if not api_key:
        print("❌ No API key found!")
        return
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    # Test different endpoints and players
    base_url = "https://api.henrikdev.xyz"
    headers = {
        'Authorization': api_key,
        'Accept': 'application/json',
        'User-Agent': 'ValorantTool/1.0'
    }
    
    # Test players that are more likely to exist
    test_cases = [
        ("TenZ", "SEN"),
        ("Shroud", "C9"),
        ("Hiko", "100T"),
        ("ScreaM", "TL"),
        ("s0m", "NRG")
    ]
    
    print("\n🧪 Testing different players...")
    
    for name, tag in test_cases:
        print(f"\n🔍 Testing: {name}#{tag}")
        
        # Test account endpoint
        url = f"{base_url}/valorant/v2/account/{name}/{tag}"
        print(f"📡 URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success! Player found:")
                account_data = data.get('data', {})
                print(f"   PUUID: {account_data.get('puuid', 'N/A')}")
                print(f"   Region: {account_data.get('region', 'N/A')}")
                print(f"   Level: {account_data.get('account_level', 'N/A')}")
                
                # Test MMR endpoint
                puuid = account_data.get('puuid')
                if puuid:
                    print(f"\n🏆 Testing MMR for {name}#{tag}...")
                    mmr_url = f"{base_url}/valorant/v3/mmr/na/pc/{name}/{tag}"
                    mmr_response = requests.get(mmr_url, headers=headers, timeout=10)
                    print(f"📊 MMR Status Code: {mmr_response.status_code}")
                    
                    if mmr_response.status_code == 200:
                        mmr_data = mmr_response.json()
                        mmr_info = mmr_data.get('data', {})
                        current_tier = mmr_info.get('currenttierpatched', 'Unknown')
                        rr = mmr_info.get('ranking_in_tier', 0)
                        print(f"✅ Rank: {current_tier} ({rr} RR)")
                    else:
                        print(f"❌ MMR request failed: {mmr_response.text[:200]}")
                
                break  # Found a working player, stop testing
                
            elif response.status_code == 404:
                print(f"❌ Player not found")
            elif response.status_code == 401:
                print(f"❌ Unauthorized - check API key")
                break
            elif response.status_code == 429:
                print(f"❌ Rate limited")
                break
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            break
    
    # Test API status
    print(f"\n🌐 Testing API status...")
    status_url = f"{base_url}/valorant/v1/status"
    try:
        response = requests.get(status_url, headers=headers, timeout=10)
        print(f"📊 Status endpoint: {response.status_code}")
        if response.status_code == 200:
            print("✅ API is operational")
        else:
            print(f"❌ API status issue: {response.text[:200]}")
    except Exception as e:
        print(f"❌ Status check failed: {e}")

if __name__ == "__main__":
    test_api_directly()
