"""
Valorant Local Client API interface for detecting live matches.
This module handles communication with the local Valorant client to get live match data.
"""

import requests
import json
import base64
import psutil
import re
import os
import logging
from typing import Optional, Dict, List, Any
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# Disable SSL warnings for local client connections
urllib3.disable_warnings(InsecureRequestWarning)

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class ValorantClientError(Exception):
    """Exception raised when there's an error with the Valorant client connection."""
    pass


class ValorantLocalClient:
    """Interface to communicate with the local Valorant client."""
    
    def __init__(self):
        self.base_url = None
        self.headers = None
        self.session = requests.Session()
        self.session.verify = False  # Local client uses self-signed certificates
        
    def _find_valorant_process(self) -> Optional[Dict[str, str]]:
        """
        Find the running Valorant process and extract connection details.

        Returns:
            Dictionary with 'port', 'password', and 'pid' if found, None otherwise
        """
        logger.info("Searching for Valorant process...")
        valorant_processes = []

        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                        valorant_processes.append(proc.info)
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        logger.info(f"Found Valorant process (PID: {proc.info['pid']})")
                        logger.debug(f"Command line: {cmdline}")

                        # Extract port from command line (try both formats)
                        port_match = re.search(r'-remoting-app-port=(\d+)', cmdline) or re.search(r'--app-port=(\d+)', cmdline)

                        # Extract auth token from command line - improved regex to handle base64 characters
                        # Valorant tokens can contain =, +, /, and other base64 characters
                        token_match = (re.search(r'-remoting-auth-token=([a-zA-Z0-9_=+/.-]+)', cmdline) or
                                     re.search(r'--app-token=([a-zA-Z0-9_=+/.-]+)', cmdline))

                        logger.info(f"Command line (first 200 chars): {cmdline[:200]}...")
                        logger.info(f"Port match: {port_match.group(1) if port_match else 'None'}")

                        if token_match:
                            token = token_match.group(1)
                            # Log masked token for debugging (show first 8 and last 4 characters)
                            masked_token = f"{token[:8]}...{token[-4:]}" if len(token) > 12 else "***"
                            logger.info(f"Token found: {masked_token} (length: {len(token)})")
                        else:
                            logger.warning("Token match: None")

                        # Additional debugging for regex matching
                        if not port_match:
                            logger.warning(f"Port regex failed. Searching in command line...")
                            # Look for any port-like patterns
                            port_patterns = re.findall(r'port[=\s]+(\d+)', cmdline, re.IGNORECASE)
                            if port_patterns:
                                logger.info(f"Found potential ports: {port_patterns}")

                        if not token_match:
                            logger.warning(f"Token regex failed. Searching for auth patterns...")
                            # Try to find the token manually with different approaches
                            if '-remoting-auth-token=' in cmdline:
                                token_start = cmdline.find('-remoting-auth-token=') + len('-remoting-auth-token=')
                                # Find the end of the token (next space or end of string)
                                token_end = cmdline.find(' ', token_start)
                                if token_end == -1:
                                    token_end = len(cmdline)
                                token_part = cmdline[token_start:token_end]
                                logger.info(f"Manual token extraction: {token_part[:8]}...{token_part[-4:]} (length: {len(token_part)})")

                                # Try to use the manually extracted token
                                if len(token_part) > 10:  # Reasonable token length
                                    token_match = type('Match', (), {'group': lambda _, n: token_part})()
                                    logger.info("Using manually extracted token")

                            # Also check for other auth patterns
                            auth_patterns = re.findall(r'auth[=\s]+([a-zA-Z0-9_=+/.-]{10,})', cmdline, re.IGNORECASE)
                            if auth_patterns:
                                logger.debug(f"Found potential auth tokens: {len(auth_patterns)} patterns")


                        if port_match and token_match:
                            try:
                                # Get PID directly from the process object to avoid race conditions
                                pid = proc.pid if hasattr(proc, 'pid') else proc.info.get('pid', 'unknown')
                                result = {
                                    'port': port_match.group(1),
                                    'password': token_match.group(1),
                                    'pid': str(pid)
                                }
                                logger.info(f"Successfully extracted connection details: port={result['port']}, pid={result['pid']}")
                                return result
                            except (KeyError, AttributeError) as e:
                                logger.error(f"Error accessing process info: {e}")
                                continue
                        else:
                            logger.warning(f"Valorant process found but missing connection details (port: {bool(port_match)}, token: {bool(token_match)})")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                    logger.debug(f"Process access error: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error finding Valorant process: {e}")

        if not valorant_processes:
            logger.warning("No Valorant processes found")
        else:
            logger.warning(f"Found {len(valorant_processes)} Valorant process(es) but none had valid connection details")

        return None
    
    def connect(self, retry_count: int = 3) -> bool:
        """
        Connect to the local Valorant client with retry mechanism.

        Args:
            retry_count: Number of times to retry connection

        Returns:
            True if connection successful, False otherwise
        """
        logger.info("Attempting to connect to Valorant client...")

        for attempt in range(retry_count):
            if attempt > 0:
                logger.info(f"Connection attempt {attempt + 1}/{retry_count}")
                import time
                time.sleep(2)  # Wait 2 seconds between attempts

            client_info = self._find_valorant_process()
            if not client_info:
                logger.error("Cannot connect: No Valorant process with valid connection details found")
                continue

            port = client_info['port']
            password = client_info['password']

            # Set up connection details
            self.base_url = f"https://127.0.0.1:{port}"
            logger.info(f"Connecting to: {self.base_url}")

            # Create authorization header
            auth_string = f"riot:{password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

            self.headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # Try connection
            if self._test_connection():
                return True

            logger.warning(f"Connection attempt {attempt + 1} failed")

        logger.error("All connection attempts failed")
        return False

    def _test_connection(self) -> bool:
        """Test connection to Valorant client with multiple endpoints."""

        # Test connection with multiple endpoints and better error handling
        test_endpoints = [
            "/entitlements/v1/token",
            "/chat/v1/session",
            "/riotclient/region-locale"
        ]

        logger.info(f"Testing connection to: {self.base_url}")
        if self.headers and 'Authorization' in self.headers:
            auth_header = self.headers['Authorization']
            logger.info(f"Using auth header: {auth_header[:20]}...")
        else:
            logger.warning("No authorization header found")

        for endpoint in test_endpoints:
            test_url = f"{self.base_url}{endpoint}"
            logger.info(f"Testing endpoint: {endpoint}")

            try:
                response = self.session.get(
                    test_url,
                    headers=self.headers,
                    timeout=10,  # Increased timeout
                    verify=False  # Explicitly disable SSL verification
                )
                logger.info(f"Endpoint {endpoint} response: {response.status_code}")

                if response.status_code == 200:
                    logger.info(f"Successfully connected to Valorant client via {endpoint}")
                    return True
                elif response.status_code == 401:
                    logger.error(f"Authentication failed for {endpoint} - check auth token")
                elif response.status_code == 403:
                    logger.error(f"Access forbidden for {endpoint} - check permissions")
                else:
                    logger.warning(f"Endpoint {endpoint} returned {response.status_code}: {response.text[:100]}")

            except requests.exceptions.ConnectionError as e:
                logger.error(f"Connection error for {endpoint}: {e}")
                if "Connection refused" in str(e):
                    logger.error("The Valorant client is not accepting connections on this port")
                elif "No connection could be made" in str(e):
                    logger.error("Cannot reach the Valorant client - check if it's running and accessible")
            except requests.exceptions.Timeout as e:
                logger.error(f"Connection timeout for {endpoint}: {e}")
            except requests.exceptions.SSLError as e:
                logger.error(f"SSL error for {endpoint}: {e}")
            except Exception as e:
                logger.error(f"Unexpected error for {endpoint}: {e}")

        logger.error("Failed to connect to any Valorant client endpoints")

        # Additional diagnostic information
        self._diagnose_connection_issue()
        return False

    def _diagnose_connection_issue(self):
        """Provide diagnostic information for connection issues."""
        logger.info("=== CONNECTION DIAGNOSIS ===")

        # Check if port is actually listening
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', int(self.base_url.split(':')[-1])))
            sock.close()

            if result == 0:
                logger.info("✅ Port is listening - this might be an authentication issue")
            else:
                logger.error("❌ Port is not listening - Valorant API server is not running")
        except Exception as e:
            logger.error(f"Port check failed: {e}")

        # Provide troubleshooting steps
        logger.info("🔧 TROUBLESHOOTING STEPS:")
        logger.info("1. Restart Valorant completely (close game + Riot Client)")
        logger.info("2. Wait for Valorant to fully load (past main menu)")
        logger.info("3. Try entering Practice Range or a match")
        logger.info("4. Run both Valorant and this app as Administrator")
        logger.info("5. Check if other Valorant tools are running")
        logger.info("==========================")
    
    def is_connected(self) -> bool:
        """Check if we're still connected to the client."""
        if not self.base_url or not self.headers:
            return False
            
        try:
            response = self.session.get(
                f"{self.base_url}/entitlements/v1/token",
                headers=self.headers,
                timeout=2
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_current_game_state(self) -> Optional[str]:
        """
        Get the current game state by checking multiple endpoints.

        Returns:
            Game state string ('PREGAME', 'INGAME', 'MENUS', etc.) or None
        """
        if not self.is_connected():
            return None

        try:
            # First, check if we're in an active match (INGAME)
            current_match = self.get_current_match()
            if current_match:
                logger.info("Player is in an active match (INGAME)")
                return 'INGAME'

            # Then check if we're in pregame
            pregame_match = self.get_pregame_match()
            if pregame_match:
                logger.info("Player is in pregame")
                return 'PREGAME'

            # Fall back to chat session state for other states
            response = self.session.get(
                f"{self.base_url}/chat/v1/session",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                chat_state = data.get('state', 'MENUS')
                logger.info(f"Chat session state: {chat_state}")
                return chat_state

        except Exception as e:
            logger.error(f"Error getting game state: {e}")

        return None

    def get_detailed_game_state(self) -> Dict[str, Any]:
        """
        Get detailed information about the current game state for debugging.

        Returns:
            Dictionary with detailed state information
        """
        state_info = {
            'connected': self.is_connected(),
            'chat_session_state': None,
            'has_current_match': False,
            'has_pregame_match': False,
            'current_match_data': None,
            'pregame_match_data': None,
            'determined_state': None,
            'errors': []
        }

        if not state_info['connected']:
            state_info['errors'].append("Not connected to Valorant client")
            return state_info

        try:
            # Check chat session
            response = self.session.get(
                f"{self.base_url}/chat/v1/session",
                headers=self.headers,
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                state_info['chat_session_state'] = data.get('state', 'UNKNOWN')
            else:
                state_info['errors'].append(f"Chat session request failed: {response.status_code}")
        except Exception as e:
            state_info['errors'].append(f"Chat session error: {e}")

        try:
            # Check current match
            current_match = self.get_current_match()
            if current_match:
                state_info['has_current_match'] = True
                state_info['current_match_data'] = {
                    'match_id': current_match.get('MatchID', 'Unknown'),
                    'game_state': current_match.get('GameState', 'Unknown'),
                    'map': current_match.get('MapID', 'Unknown')
                }
        except Exception as e:
            state_info['errors'].append(f"Current match error: {e}")

        try:
            # Check pregame match
            pregame_match = self.get_pregame_match()
            if pregame_match:
                state_info['has_pregame_match'] = True
                state_info['pregame_match_data'] = {
                    'match_id': pregame_match.get('MatchID', 'Unknown'),
                    'state': pregame_match.get('State', 'Unknown'),
                    'map': pregame_match.get('MapID', 'Unknown')
                }
        except Exception as e:
            state_info['errors'].append(f"Pregame match error: {e}")

        # Determine the actual state
        if state_info['has_current_match']:
            state_info['determined_state'] = 'INGAME'
        elif state_info['has_pregame_match']:
            state_info['determined_state'] = 'PREGAME'
        else:
            state_info['determined_state'] = state_info['chat_session_state'] or 'MENUS'

        return state_info

    def get_pregame_match(self) -> Optional[Dict[str, Any]]:
        """
        Get pregame match information.

        Returns:
            Pregame match data or None
        """
        if not self.is_connected():
            return None

        try:
            # First, try to get the player's current pregame match ID
            player_uuid = self._get_player_uuid()
            if not player_uuid:
                return None

            response = self.session.get(
                f"{self.base_url}/pregame/v1/players/{player_uuid}",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                player_data = response.json()
                match_id = player_data.get('MatchID')

                if match_id:
                    # Get the full match details
                    match_response = self.session.get(
                        f"{self.base_url}/pregame/v1/matches/{match_id}",
                        headers=self.headers,
                        timeout=5
                    )

                    if match_response.status_code == 200:
                        return match_response.json()

        except Exception as e:
            print(f"Error getting pregame match: {e}")

        return None
    
    def get_current_match(self) -> Optional[Dict[str, Any]]:
        """
        Get current match information.

        Returns:
            Current match data or None
        """
        if not self.is_connected():
            return None

        try:
            # First, try to get the player's current match ID
            player_uuid = self._get_player_uuid()
            if not player_uuid:
                return None

            response = self.session.get(
                f"{self.base_url}/core-game/v1/players/{player_uuid}",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                player_data = response.json()
                match_id = player_data.get('MatchID')

                if match_id:
                    # Get the full match details
                    match_response = self.session.get(
                        f"{self.base_url}/core-game/v1/matches/{match_id}",
                        headers=self.headers,
                        timeout=5
                    )

                    if match_response.status_code == 200:
                        return match_response.json()

        except Exception as e:
            print(f"Error getting current match: {e}")

        return None
    
    def get_player_loadout(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get player loadout information.
        
        Args:
            player_uuid: Player's UUID
            
        Returns:
            Player loadout data or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/personalization/v2/players/{player_uuid}/playerloadout",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting player loadout: {e}")
            
        return None
    
    def get_match_details(self, match_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed match information.
        
        Args:
            match_id: Match ID
            
        Returns:
            Match details or None
        """
        if not self.is_connected():
            return None
            
        try:
            response = self.session.get(
                f"{self.base_url}/core-game/v1/matches/{match_id}",
                headers=self.headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            print(f"Error getting match details: {e}")
            
        return None

    def get_party_info(self) -> Optional[Dict[str, Any]]:
        """
        Get current party information.

        Returns:
            Party data or None
        """
        if not self.is_connected():
            return None

        try:
            response = self.session.get(
                f"{self.base_url}/parties/v1/players/{self._get_player_uuid()}",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                return response.json()
            else:
                print(f"Party info request failed with status: {response.status_code}")

        except Exception as e:
            print(f"Error getting party info: {e}")

        return None

    def get_presence(self) -> Optional[Dict[str, Any]]:
        """
        Get presence information.

        Returns:
            Presence data or None
        """
        if not self.is_connected():
            return None

        try:
            response = self.session.get(
                f"{self.base_url}/chat/v4/presences",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                return response.json()

        except Exception as e:
            print(f"Error getting presence: {e}")

        return None

    def get_party_info(self) -> Optional[Dict[str, Any]]:
        """
        Get party information.

        Returns:
            Party data or None
        """
        if not self.is_connected():
            return None

        try:
            player_uuid = self._get_player_uuid()
            if not player_uuid:
                return None

            response = self.session.get(
                f"{self.base_url}/parties/v1/players/{player_uuid}",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                return response.json()

        except Exception as e:
            print(f"Error getting party info: {e}")

        return None

    def get_session_info(self) -> Optional[Dict[str, Any]]:
        """
        Get current session information.

        Returns:
            Session data or None
        """
        if not self.is_connected():
            return None

        try:
            response = self.session.get(
                f"{self.base_url}/chat/v1/session",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                return response.json()
            else:
                print(f"Session info request failed with status: {response.status_code}")

        except Exception as e:
            print(f"Error getting session info: {e}")

        return None

    def get_friends_list(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get friends list information.

        Returns:
            List of friends data or None
        """
        if not self.is_connected():
            return None

        try:
            response = self.session.get(
                f"{self.base_url}/chat/v4/friends",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                return data.get('friends', [])
            else:
                print(f"Friends list request failed with status: {response.status_code}")

        except Exception as e:
            print(f"Error getting friends list: {e}")

        return None

    def get_player_names_from_puuids(self, puuids: List[str]) -> Dict[str, str]:
        """
        Get player names from a list of PUUIDs using the name service.

        Args:
            puuids: List of player UUIDs

        Returns:
            Dictionary mapping PUUID to "GameName#TagLine"
        """
        if not self.is_connected() or not puuids:
            return {}

        try:
            # Use the name service endpoint like VALORANT-rank-yoinker
            response = self.session.put(
                f"{self.base_url}/name-service/v2/players",
                headers=self.headers,
                json=puuids,
                timeout=5,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    name_dict = {}
                    for player in data:
                        if 'Subject' in player and 'GameName' in player and 'TagLine' in player:
                            puuid = player['Subject']
                            name = f"{player['GameName']}#{player['TagLine']}"
                            name_dict[puuid] = name
                    return name_dict
            else:
                print(f"Name service request failed with status: {response.status_code}")

        except Exception as e:
            print(f"Error getting player names: {e}")

        return {}

    def _get_player_uuid(self) -> Optional[str]:
        """
        Get the current player's UUID.

        Returns:
            Player UUID or None
        """
        try:
            # Try to get player UUID from entitlements
            response = self.session.get(
                f"{self.base_url}/entitlements/v1/token",
                headers=self.headers,
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                return data.get('subject')

        except Exception as e:
            print(f"Error getting player UUID: {e}")

        return None

    def debug_connection(self) -> Dict[str, Any]:
        """
        Debug connection issues and return detailed information.

        Returns:
            Dictionary with debug information
        """
        debug_info = {
            'valorant_running': False,
            'process_found': False,
            'connection_details_found': False,
            'connection_successful': False,
            'processes': [],
            'connection_details': {},
            'test_results': {},
            'errors': []
        }

        try:
            # Check if Valorant is running
            valorant_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        valorant_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline_preview': cmdline[:200] + '...' if len(cmdline) > 200 else cmdline,
                            'cmdline_length': len(cmdline)
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            debug_info['valorant_running'] = len(valorant_processes) > 0
            debug_info['processes'] = valorant_processes
            debug_info['process_found'] = len(valorant_processes) > 0

            if valorant_processes:
                # Try to find connection details
                client_info = self._find_valorant_process()
                debug_info['connection_details_found'] = client_info is not None

                if client_info:
                    # Store connection details (mask the password)
                    debug_info['connection_details'] = {
                        'port': client_info['port'],
                        'password_length': len(client_info['password']),
                        'password_preview': f"{client_info['password'][:8]}...{client_info['password'][-4:]}" if len(client_info['password']) > 12 else "***",
                        'pid': client_info['pid']
                    }

                    # Test connection manually without using self.connect()
                    base_url = f"https://127.0.0.1:{client_info['port']}"
                    auth_string = f"riot:{client_info['password']}"
                    auth_bytes = auth_string.encode('ascii')
                    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

                    headers = {
                        'Authorization': f'Basic {auth_b64}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }

                    # Test multiple endpoints
                    test_endpoints = [
                        "/entitlements/v1/token",
                        "/chat/v1/session",
                        "/riotclient/region-locale"
                    ]

                    session = requests.Session()
                    session.verify = False

                    for endpoint in test_endpoints:
                        test_url = f"{base_url}{endpoint}"
                        try:
                            response = session.get(test_url, headers=headers, timeout=5)
                            debug_info['test_results'][endpoint] = {
                                'status_code': response.status_code,
                                'success': response.status_code == 200,
                                'response_preview': response.text[:100] if response.text else ''
                            }
                            if response.status_code == 200:
                                debug_info['connection_successful'] = True
                        except Exception as e:
                            debug_info['test_results'][endpoint] = {
                                'error': str(e),
                                'success': False
                            }

                else:
                    debug_info['errors'].append("No connection details found in command line arguments")

                    # Try to extract details manually for debugging
                    if valorant_processes:
                        cmdline = valorant_processes[0]['cmdline_preview']
                        port_patterns = re.findall(r'port[=\s]*(\d+)', cmdline, re.IGNORECASE)
                        auth_patterns = re.findall(r'auth[=\s]*([a-zA-Z0-9_=+/.-]{10,})', cmdline, re.IGNORECASE)

                        debug_info['manual_extraction'] = {
                            'potential_ports': port_patterns,
                            'potential_auth_count': len(auth_patterns)
                        }
            else:
                debug_info['errors'].append("No Valorant processes found")

        except Exception as e:
            debug_info['errors'].append(f"Debug error: {str(e)}")

        return debug_info


def is_valorant_running() -> bool:
    """Check if Valorant is currently running."""
    try:
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] == 'VALORANT-Win64-Shipping.exe':
                return True
    except Exception:
        pass
    return False
