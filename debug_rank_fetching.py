#!/usr/bin/env python3
"""
Debug script to test HenrikDev API rank fetching functionality.
This will help identify why player information and ranks aren't being retrieved.
"""

import sys
import os
from typing import Optional

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api_client import ValorantAPI, APIError, RateLimitError
from src.config import config
from src.utils import print_error, print_success, print_info, print_warning

def test_api_connection():
    """Test basic API connectivity and configuration."""
    print_info("🔧 Testing HenrikDev API Configuration...")
    
    # Check API key
    if not config.api_key:
        print_error("❌ No API key found in .env file!")
        return False
    
    print_success(f"✅ API key found: {config.api_key[:10]}...")
    print_info(f"📍 Default region: {config.default_region}")
    print_info(f"💻 Default platform: {config.default_platform}")
    
    return True

def test_simple_lookup():
    """Test a simple player lookup."""
    print_info("\n🔍 Testing Simple Player Lookup...")
    
    # Test with a known player (you can change this)
    test_name = input("Enter a player name to test (or press Enter for 'TenZ'): ").strip()
    test_tag = input("Enter the tag (or press Enter for 'SEN'): ").strip()
    
    if not test_name:
        test_name = "TenZ"
    if not test_tag:
        test_tag = "SEN"
    
    print_info(f"Testing lookup for: {test_name}#{test_tag}")
    
    try:
        api = ValorantAPI()
        
        # Test account lookup
        print_info("📋 Testing account lookup...")
        player = api.get_account_info(test_name, test_tag)
        
        if player:
            print_success(f"✅ Account found!")
            print_info(f"   PUUID: {player.puuid}")
            print_info(f"   Region: {player.region}")
            print_info(f"   Level: {player.account_level}")
            print_info(f"   Platform: {player.platform}")
        else:
            print_error("❌ Account not found!")
            return False
        
        # Test rank lookup
        print_info("🏆 Testing rank lookup...")
        current_rank, peak_rank = api.get_mmr_info(test_name, test_tag)
        
        if current_rank:
            print_success(f"✅ Current rank found: {current_rank.display_rank}")
        else:
            print_warning("⚠️ No current rank data found (player might be unranked)")
        
        if peak_rank:
            print_success(f"✅ Peak rank found: {peak_rank}")
        else:
            print_warning("⚠️ No peak rank data found")
        
        return True
        
    except RateLimitError as e:
        print_error(f"❌ Rate limit error: {e}")
        print_info("💡 Try again in a few minutes")
        return False
    except APIError as e:
        print_error(f"❌ API error: {e}")
        return False
    except Exception as e:
        print_error(f"❌ Unexpected error: {e}")
        return False

def test_batch_lookup():
    """Test batch player lookup functionality."""
    print_info("\n👥 Testing Batch Player Lookup...")
    
    # Test with multiple players
    test_players = [
        ("TenZ", "SEN"),
        ("Shroud", "C9"),
        ("s1mple", "NAVI")  # This might not exist in Valorant
    ]
    
    try:
        api = ValorantAPI()
        
        for name, tag in test_players:
            print_info(f"Testing: {name}#{tag}")
            try:
                player = api.get_complete_player_info(name, tag)
                if player:
                    rank_display = player.display_rank if player.current_rank else "No rank data"
                    print_success(f"  ✅ Found: Level {player.account_level}, Rank: {rank_display}")
                else:
                    print_warning(f"  ⚠️ Not found")
            except Exception as e:
                print_error(f"  ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print_error(f"❌ Batch lookup failed: {e}")
        return False

def test_api_endpoints():
    """Test individual API endpoints."""
    print_info("\n🌐 Testing API Endpoints...")
    
    try:
        api = ValorantAPI()
        
        # Test a simple endpoint
        print_info("Testing account endpoint...")
        response = api._make_request("/valorant/v2/account/TenZ/SEN")
        
        if response:
            print_success("✅ API endpoint responding correctly")
            return True
        else:
            print_error("❌ API endpoint not responding")
            return False
            
    except RateLimitError:
        print_error("❌ Rate limited - API key might be over quota")
        return False
    except APIError as e:
        print_error(f"❌ API error: {e}")
        return False
    except Exception as e:
        print_error(f"❌ Connection error: {e}")
        print_info("💡 Check your internet connection")
        return False

def main():
    """Main debug function."""
    print_info("🎯 Valorant Rank Fetching Debug Tool")
    print_info("=" * 50)
    
    # Test 1: Configuration
    if not test_api_connection():
        print_error("\n❌ Configuration test failed!")
        return
    
    # Test 2: API endpoints
    if not test_api_endpoints():
        print_error("\n❌ API endpoint test failed!")
        return
    
    # Test 3: Simple lookup
    if not test_simple_lookup():
        print_error("\n❌ Simple lookup test failed!")
        return
    
    # Test 4: Batch lookup
    test_batch_lookup()
    
    print_info("\n" + "=" * 50)
    print_success("🎉 Debug tests completed!")
    print_info("\n💡 If all tests passed but you're still having issues:")
    print_info("   1. Check that you're using the correct player names/tags")
    print_info("   2. Some players might not have competitive rank data")
    print_info("   3. Make sure your API key hasn't exceeded rate limits")
    print_info("   4. Try different regions if players are from other regions")

if __name__ == "__main__":
    main()
