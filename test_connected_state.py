#!/usr/bin/env python3
"""
Test what data is available in the 'connected' state.
"""

import sys
import os
import json

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.valorant_client import ValorantLocalClient

def test_connected_state_data():
    """Test what data we can get in connected state."""
    print("🔍 Testing Connected State Data")
    print("=" * 50)
    
    client = ValorantLocalClient()
    if not client.connect():
        print("❌ Failed to connect")
        return
    
    print("✅ Connected to Valorant client")
    print(f"🎯 Current state: {client.get_current_game_state()}")
    
    # Test all available endpoints
    endpoints_to_test = [
        ("Party Info", lambda: client.get_party_info()),
        ("Presence Info", lambda: client.get_presence()),
        ("Session Info", lambda: client.get_session_info()),
        ("Friends List", lambda: client.get_friends_list()),
        ("Current Match", lambda: client.get_current_match()),
        ("Pregame Match", lambda: client.get_pregame_match()),
    ]
    
    for name, func in endpoints_to_test:
        print(f"\n📊 Testing {name}...")
        try:
            data = func()
            if data:
                print(f"✅ {name} - Data found!")
                
                # Show relevant info based on endpoint
                if name == "Party Info":
                    members = data.get('Members', [])
                    print(f"   Party members: {len(members)}")
                    if members:
                        print("   Member PUUIDs:")
                        for i, member in enumerate(members[:3]):
                            puuid = member.get('Subject', 'Unknown')
                            print(f"     {i+1}. {puuid}")
                
                elif name == "Presence Info":
                    presences = data.get('presences', [])
                    print(f"   Presences: {len(presences)}")
                    if presences:
                        print("   First few presences:")
                        for i, presence in enumerate(presences[:3]):
                            puuid = presence.get('puuid', 'Unknown')
                            state = presence.get('state', 'Unknown')
                            print(f"     {i+1}. {puuid} - {state}")
                
                elif name == "Session Info":
                    state = data.get('state', 'Unknown')
                    print(f"   Session state: {state}")
                
                elif name == "Current Match" or name == "Pregame Match":
                    match_id = data.get('MatchID', 'Unknown')
                    map_id = data.get('MapID', 'Unknown')
                    mode = data.get('Mode', 'Unknown')
                    print(f"   Match ID: {match_id}")
                    print(f"   Map: {map_id}")
                    print(f"   Mode: {mode}")
                    
                    # Check for players
                    players = data.get('Players', [])
                    ally_team = data.get('AllyTeam', {}).get('Players', [])
                    enemy_team = data.get('EnemyTeam', {}).get('Players', [])
                    
                    total_players = len(players) + len(ally_team) + len(enemy_team)
                    print(f"   Total players found: {total_players}")
                
            else:
                print(f"⚠️ {name} - No data")
                
        except Exception as e:
            print(f"❌ {name} - Error: {e}")
    
    # Test name resolution if we have party members
    print(f"\n👤 Testing Name Resolution...")
    try:
        party_data = client.get_party_info()
        if party_data and 'Members' in party_data:
            members = party_data['Members']
            puuids = [member.get('Subject') for member in members if member.get('Subject')]
            
            if puuids:
                print(f"   Found {len(puuids)} party member PUUIDs")
                name_dict = client.get_player_names_from_puuids(puuids)
                
                if name_dict:
                    print("✅ Name resolution works!")
                    for puuid, name in name_dict.items():
                        print(f"     {puuid[:8]}... -> {name}")
                else:
                    print("❌ Name resolution failed")
            else:
                print("⚠️ No PUUIDs found in party data")
        else:
            print("⚠️ No party data available")
            
    except Exception as e:
        print(f"❌ Name resolution error: {e}")

def analyze_game_mode():
    """Try to determine what game mode you're in."""
    print(f"\n🎮 Analyzing Current Game Mode...")
    print("=" * 50)
    
    client = ValorantLocalClient()
    if not client.connect():
        return
    
    # Get detailed state info
    detailed_state = client.get_detailed_game_state()
    print("📊 Detailed game state:")
    for key, value in detailed_state.items():
        if key != 'errors':
            print(f"   {key}: {value}")
    
    if detailed_state.get('errors'):
        print("   Errors:")
        for error in detailed_state['errors']:
            print(f"     - {error}")
    
    # Try to get presence data to determine mode
    try:
        presence_data = client.get_presence()
        if presence_data and 'presences' in presence_data:
            print(f"\n🔍 Analyzing presence data for game mode clues...")
            
            # Look for your own presence
            player_uuid = client._get_player_uuid()
            if player_uuid:
                for presence in presence_data['presences']:
                    if presence.get('puuid') == player_uuid:
                        print(f"   Your presence state: {presence.get('state', 'Unknown')}")
                        
                        # Try to decode private presence if it exists
                        private = presence.get('private')
                        if private:
                            try:
                                # Private data might be base64 encoded
                                import base64
                                decoded = base64.b64decode(private).decode('utf-8')
                                private_data = json.loads(decoded)
                                print(f"   Private presence data: {private_data}")
                            except:
                                print(f"   Private presence (raw): {private[:100]}...")
                        break
    except Exception as e:
        print(f"❌ Error analyzing presence: {e}")

def main():
    """Main function."""
    print("🎯 Connected State Analysis Tool")
    print("This will help identify why player data isn't available")
    print()
    
    test_connected_state_data()
    analyze_game_mode()
    
    print(f"\n" + "=" * 50)
    print("💡 Summary:")
    print("   - If you're in Deathmatch: Player data is not available")
    print("   - If you're in Practice Range: No teammates to detect")
    print("   - If you're in Competitive/Unrated: Try entering agent select")
    print("   - If party data shows members: Name resolution should work")
    print("\n🎯 For best results:")
    print("   1. Queue for Competitive or Unrated")
    print("   2. Wait until agent select screen")
    print("   3. Run the live detection again")

if __name__ == "__main__":
    main()
