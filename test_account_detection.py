#!/usr/bin/env python3
"""
Test automatic account detection.
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.valorant_client import ValorantLocalClient, is_valorant_running
from src.api_client import Valorant<PERSON><PERSON>
from src.live_match_detector import LiveMatchDetector

def test_account_detection():
    """Test the automatic account detection."""
    print("🔍 Testing Automatic Account Detection")
    print("=" * 60)
    
    if not is_valorant_running():
        print("❌ Valorant is not running!")
        return
    
    print("✅ Valorant is running")
    
    try:
        # Initialize the system
        api = ValorantAPI()
        detector = LiveMatchDetector(api)
        
        print("✅ Live match detector initialized")
        
        # Test account detection directly
        print("\n🔍 Testing account detection methods...")
        
        account_info = detector._get_current_account_info()
        if account_info:
            name = account_info.get('name', 'Unknown')
            tag = account_info.get('tag', 'Unknown')
            print(f"✅ Account detected: {name}#{tag}")
            
            # Test rank lookup with detected account
            print(f"\n🏆 Testing rank lookup for {name}#{tag}...")
            try:
                player_info = api.get_player_full_info(name, tag)
                if player_info:
                    print(f"✅ Rank found: {player_info.display_rank}")
                    print(f"✅ Level: {player_info.account_level}")
                    print(f"✅ Peak: {player_info.display_peak}")
                else:
                    print("❌ Could not get rank info")
            except Exception as e:
                print(f"❌ Rank lookup error: {e}")
        else:
            print("❌ Could not detect current account")
            
            # Try manual methods
            print("\n🔧 Trying manual detection methods...")
            
            client = ValorantLocalClient()
            if client.connect():
                player_uuid = client._get_player_uuid()
                if player_uuid:
                    print(f"✅ Player UUID: {player_uuid[:8]}...")
                    
                    # Try name service
                    name_dict = client.get_player_names_from_puuids([player_uuid])
                    if name_dict and player_uuid in name_dict:
                        full_name = name_dict[player_uuid]
                        print(f"✅ Name from service: {full_name}")
                    else:
                        print("❌ Name service failed")
                else:
                    print("❌ Could not get player UUID")
            else:
                print("❌ Could not connect to client")
        
        return account_info
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Main function."""
    print("🎯 Account Detection Test")
    print("This will test automatic detection of your current Valorant account")
    print()
    
    account_info = test_account_detection()
    
    print("\n" + "=" * 60)
    if account_info:
        name = account_info.get('name', 'Unknown')
        tag = account_info.get('tag', 'Unknown')
        print(f"🎉 Success! Detected account: {name}#{tag}")
        print("\n💡 The live detection will now automatically use this account")
        print("   instead of hardcoded 'rileyx#umi'")
    else:
        print("❌ Account detection failed")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure you're logged into Valorant")
        print("   2. Try entering a match or Practice Range")
        print("   3. Restart Valorant if needed")

if __name__ == "__main__":
    main()
