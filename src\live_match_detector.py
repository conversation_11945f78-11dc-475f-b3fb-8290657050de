"""
Live Match Detector for Valorant.
Automatically detects when the user is in a live match and extracts player information.
"""

import time
import threading
import base64
import json
import binascii
from typing import Optional, Dict, List, Any, Callable

try:
    from .valorant_client import ValorantLocalClient, is_valorant_running
    from .api_client import ValorantAP<PERSON>
except ImportError:
    # Handle relative imports when running as script
    from valorant_client import ValorantLocalClient, is_valorant_running
    from api_client import ValorantAPI


class LiveMatchDetector:
    """Detects and monitors live Valorant matches."""
    
    def __init__(self, api_client: ValorantAPI):
        self.api_client = api_client
        self.client = ValorantLocalClient()
        self.is_monitoring = False
        self.current_match_data = None
        self.last_game_state = None
        self.callbacks = []
        self.monitor_thread = None
        
    def add_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a callback function to be called when match data is updated."""
        self.callbacks.append(callback)
        
    def remove_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Remove a callback function."""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            
    def _notify_callbacks(self, match_data: Dict[str, Any]):
        """Notify all registered callbacks with new match data."""
        for callback in self.callbacks:
            try:
                callback(match_data)
            except Exception as e:
                print(f"Error in callback: {e}")
    
    def start_monitoring(self) -> bool:
        """
        Start monitoring for live matches.
        
        Returns:
            True if monitoring started successfully, False otherwise
        """
        if self.is_monitoring:
            return True
            
        if not is_valorant_running():
            return False
            
        if not self.client.connect():
            return False
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        return True
    
    def stop_monitoring(self):
        """Stop monitoring for live matches."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
            
    def _monitor_loop(self):
        """Main monitoring loop that runs in a separate thread."""
        while self.is_monitoring:
            try:
                if not is_valorant_running():
                    self.is_monitoring = False
                    break
                    
                if not self.client.is_connected():
                    if not self.client.connect():
                        time.sleep(5)
                        continue
                
                # Check current game state
                game_state = self.client.get_current_game_state()
                
                if game_state != self.last_game_state:
                    self.last_game_state = game_state
                    print(f"Game state changed to: {game_state}")
                
                # Handle different game states
                if game_state == 'PREGAME':
                    self._handle_pregame()
                elif game_state == 'INGAME':
                    self._handle_ingame()
                elif game_state == 'connected':
                    # Handle connected state (deathmatch, etc.)
                    self._handle_connected()
                elif game_state == 'MENUS':
                    if self.current_match_data:
                        self.current_match_data = None
                        self._notify_callbacks({'state': 'MENUS', 'players': []})
                else:
                    # Log unknown game states for debugging
                    print(f"Unknown game state detected: {game_state}")
                
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                print(f"Error in monitor loop: {e}")
                time.sleep(5)
    
    def _handle_pregame(self):
        """Handle pregame state - extract player information."""
        try:
            pregame_data = self.client.get_pregame_match()
            if not pregame_data:
                return



            players = self._extract_player_info(pregame_data)
            if players:
                match_data = {
                    'state': 'PREGAME',
                    'players': players,
                    'map': pregame_data.get('MapID', 'Unknown'),
                    'mode': pregame_data.get('Mode', 'Unknown')
                }

                if self.current_match_data != match_data:
                    self.current_match_data = match_data
                    self._notify_callbacks(match_data)

        except Exception as e:
            print(f"Error handling pregame: {e}")
    
    def _handle_ingame(self):
        """Handle in-game state - extract player information."""
        try:
            match_data = self.client.get_current_match()
            if not match_data:
                return



            players = self._extract_player_info(match_data)
            if players:
                game_data = {
                    'state': 'INGAME',
                    'players': players,
                    'map': match_data.get('MapID', 'Unknown'),
                    'mode': match_data.get('Mode', 'Unknown')
                }

                if self.current_match_data != game_data:
                    self.current_match_data = game_data
                    self._notify_callbacks(game_data)

        except Exception as e:
            print(f"Error handling ingame: {e}")

    def _handle_connected(self):
        """Handle connected state - for deathmatch, swiftplay and other game modes."""
        try:
            print("Trying to get match data in connected state...")

            # Try current match endpoint first
            match_data = self.client.get_current_match()
            print(f"Current match data: {match_data}")

            # Try pregame endpoint
            if not match_data:
                print("No current match data, trying pregame...")
                match_data = self.client.get_pregame_match()
                print(f"Pregame match data: {match_data}")

            # Try alternative endpoints for agent select detection
            if not match_data:
                print("Trying alternative endpoints for agent select...")
                # Try to get presence data which might show current activity
                presence_data = self.client.get_presence()
                print(f"Presence data: {presence_data}")

                # Parse presence data for match information
                if presence_data:
                    match_data = self._parse_presence_data(presence_data)
                    if match_data:
                        print(f"Found match data from presence: {match_data}")

                # Try to get party data which might show current queue state
                if not match_data:
                    party_data = self.client.get_party_info()
                    print(f"Party data: {party_data}")

            # Try to get game state info
            game_state_info = self.client.get_current_game_state()
            print(f"Game state info: {game_state_info}")

            # If we have match data from presence, use it
            if match_data and 'state' in match_data and 'map' in match_data and 'mode' in match_data:
                print(f"Using match data from presence: {match_data}")

                # Check if this is deathmatch
                if match_data.get('mode', '').lower() == 'deathmatch':
                    deathmatch_data = {
                        'state': 'DEATHMATCH',
                        'mode': 'Deathmatch',
                        'message': 'Deathmatch detected - Player analysis not available for this mode',
                        'players': [],
                        'map': match_data.get('map', 'Unknown')
                    }

                    if self.current_match_data != deathmatch_data:
                        self.current_match_data = deathmatch_data
                        self._notify_callbacks(deathmatch_data)
                        print("Deathmatch mode detected and reported")
                    return
                else:
                    # For other modes (like Swiftplay), try to get player information
                    print(f"Trying to get player information for {match_data.get('mode')} mode...")

                    # Try alternative player detection methods
                    alternative_data = self._try_alternative_player_detection()
                    if alternative_data and alternative_data.get('players'):
                        print(f"Found {len(alternative_data['players'])} players via alternative detection")
                        enhanced_match_data = {
                            'state': match_data.get('state', 'INGAME'),
                            'mode': match_data.get('mode', 'Unknown'),
                            'map': match_data.get('map', 'Unknown'),
                            'players': alternative_data['players']
                        }

                        if self.current_match_data != enhanced_match_data:
                            self.current_match_data = enhanced_match_data
                            self._notify_callbacks(enhanced_match_data)
                            print(f"{match_data.get('mode')} mode with {len(alternative_data['players'])} players detected and reported")
                        return
                    else:
                        # Fallback to presence data without players
                        print("No players found via alternative detection, using presence data only")
                        if self.current_match_data != match_data:
                            self.current_match_data = match_data
                            self._notify_callbacks(match_data)
                            print(f"{match_data.get('mode')} mode detected and reported (no player data)")
                        return

            # If no match data, determine the actual game mode from presence/session data
            if not match_data:
                print("No standard match data found, checking game mode from presence...")

                # Get actual game mode from session/presence data
                game_mode_info = self._get_game_mode_from_presence()

                if game_mode_info:
                    mode = game_mode_info.get('mode', 'Unknown')
                    queue_id = game_mode_info.get('queue_id', '')

                    print(f"Detected game mode: {mode} (queue_id: {queue_id})")

                    # Check if this is actually deathmatch or another mode
                    if self._is_deathmatch_mode(mode, queue_id):
                        print("Confirmed deathmatch mode - no player data available")

                        deathmatch_data = {
                            'state': 'DEATHMATCH',
                            'mode': 'Deathmatch',
                            'message': 'Deathmatch detected - Player analysis not available for this mode',
                            'players': [],
                            'map': 'Unknown'
                        }

                        if self.current_match_data != deathmatch_data:
                            self.current_match_data = deathmatch_data
                            self._notify_callbacks(deathmatch_data)
                            print("Deathmatch mode detected and reported")
                        return
                    else:
                        print(f"Detected {mode} - API doesn't provide player data for this mode")

                        # Create connected state data for Swiftplay or other modes
                        connected_data = {
                            'state': 'CONNECTED',
                            'mode': mode,
                            'message': f'{mode} detected - Player data not available via local API',
                            'players': [],
                            'map': 'Unknown'
                        }

                        if self.current_match_data != connected_data:
                            self.current_match_data = connected_data
                            self._notify_callbacks(connected_data)
                            print(f"{mode} mode detected and reported")
                        return

                # Fallback if we can't determine the mode
                print("Could not determine game mode - defaulting to unknown connected state")

                fallback_data = {
                    'state': 'CONNECTED',
                    'mode': 'Unknown',
                    'message': 'Connected to match - Player data not available',
                    'players': [],
                    'map': 'Unknown'
                }

                if self.current_match_data != fallback_data:
                    self.current_match_data = fallback_data
                    self._notify_callbacks(fallback_data)
                    print("Unknown connected mode detected and reported")
                return

            print(f"Connected state match data: {match_data}")

            players = self._extract_player_info(match_data)
            if players:
                game_data = {
                    'state': 'CONNECTED',
                    'players': players,
                    'map': match_data.get('MapID', 'Unknown'),
                    'mode': match_data.get('Mode', 'Connected')
                }

                if self.current_match_data != game_data:
                    self.current_match_data = game_data
                    self._notify_callbacks(game_data)
                    print(f"Found {len(players)} players in connected state")
            else:
                print("No players found in connected state")

        except Exception as e:
            print(f"Error handling connected state: {e}")

    def _get_game_mode_from_presence(self) -> Optional[Dict[str, Any]]:
        """
        Get game mode information from presence/session data.

        Returns:
            Dictionary with mode and queue_id information, or None if not found
        """
        try:
            # Try to get session info which might contain queue information
            session_data = self.client.get_session_info()
            if session_data:
                print(f"Session data found: {session_data}")
                # Session data might contain queue or mode information
                # This is a simplified approach - you might need to adjust based on actual API response

            # Try to get party info which might contain queue information
            party_data = self.client.get_party_info()
            if party_data:
                print(f"Party data found: {party_data}")
                # Look for queue ID or mode information in party data
                # This would need to be implemented based on actual Valorant API response structure

            # For now, return None as we need to see the actual API responses
            # to implement proper mode detection
            return None

        except Exception as e:
            print(f"Error getting game mode from presence: {e}")
            return None

    def _is_deathmatch_mode(self, mode: str, queue_id: str) -> bool:
        """
        Determine if the given mode/queue_id represents a deathmatch.

        Args:
            mode: Game mode name
            queue_id: Queue identifier

        Returns:
            True if this is deathmatch, False otherwise
        """
        # Common deathmatch identifiers
        deathmatch_modes = ['deathmatch', 'dm']
        deathmatch_queue_ids = ['deathmatch', 'dm']  # Add actual queue IDs as we discover them

        if mode and mode.lower() in deathmatch_modes:
            return True

        if queue_id and queue_id.lower() in deathmatch_queue_ids:
            return True

        return False

    def _try_alternative_player_detection(self) -> Optional[Dict[str, Any]]:
        """
        Try alternative methods to detect players in Swiftplay and other modes.

        Returns:
            Dictionary with player data and mode info, or None if not found
        """
        try:
            print("Attempting alternative player detection methods...")

            # Method 1: Try to get party members with real names
            party_data = self.client.get_party_info()
            if party_data and 'Members' in party_data:
                print(f"Found party data with {len(party_data.get('Members', []))} members")

                # Extract PUUIDs from party members
                puuids = []
                for member in party_data.get('Members', []):
                    player_uuid = member.get('Subject', '')
                    if player_uuid:
                        puuids.append(player_uuid)

                if puuids:
                    # Get real player names using the name service
                    print(f"Getting names for {len(puuids)} party members...")
                    name_dict = self.client.get_player_names_from_puuids(puuids)
                    print(f"Retrieved names: {name_dict}")

                    players = []
                    for i, member in enumerate(party_data.get('Members', [])):
                        player_uuid = member.get('Subject', '')
                        if player_uuid:
                            # Use real name if available, otherwise fallback
                            player_name = name_dict.get(player_uuid, f'Player{i+1}')

                            # Try to get rank information for this player
                            rank_info = self._get_player_rank(player_uuid)

                            players.append({
                                'uuid': player_uuid,
                                'name': player_name.split('#')[0] if '#' in player_name else player_name,
                                'tag': player_name.split('#')[1] if '#' in player_name else 'Unknown',
                                'rank': rank_info.get('rank', 'Unknown'),
                                'rr': rank_info.get('rr', 0),
                                'team': 'Unknown',
                                'agent': 'Unknown',
                                'level': rank_info.get('level', 0)
                            })

                    if players:
                        print(f"Found {len(players)} players from party data with names")
                        return {
                            'players': players,
                            'mode': 'Swiftplay',
                            'map': 'Unknown'
                        }

            # Method 2: Try to get friends list to find recent players
            try:
                friends_data = self.client.get_friends_list()
                if friends_data and isinstance(friends_data, list):
                    print(f"Found friends list with {len(friends_data)} friends")

                    # Look for friends who are currently online and in-game
                    online_friends = []
                    for friend in friends_data:
                        if friend.get('availability') == 'dnd':  # 'dnd' usually means in-game
                            online_friends.append({
                                'uuid': friend.get('puuid', ''),
                                'name': friend.get('game_name', 'Unknown'),
                                'tag': friend.get('game_tag', 'Unknown'),
                                'rank': 'Unknown',
                                'rr': 0,
                                'team': 'Unknown',
                                'agent': 'Unknown',
                                'level': 0
                            })

                    if online_friends:
                        print(f"Found {len(online_friends)} online friends who might be in the match")
                        return {
                            'players': online_friends[:10],  # Limit to 10 players max
                            'mode': 'Swiftplay',
                            'map': 'Unknown'
                        }
            except Exception as e:
                print(f"Error getting friends list: {e}")

            # Method 3: Create a minimal player entry for the current user
            try:
                player_uuid = self.client._get_player_uuid()
                if player_uuid:
                    print("Creating minimal player data for current user")
                    return {
                        'players': [{
                            'uuid': player_uuid,
                            'name': 'You',
                            'tag': 'Player',
                            'rank': 'Unknown',
                            'rr': 0,
                            'team': 'Unknown',
                            'agent': 'Unknown',
                            'level': 0
                        }],
                        'mode': 'Swiftplay',
                        'map': 'Unknown'
                    }
            except Exception as e:
                print(f"Error getting current player UUID: {e}")

            print("No alternative player detection methods successful")
            return None

        except Exception as e:
            print(f"Error in alternative player detection: {e}")
            return None

    def _get_player_rank(self, player_uuid: str) -> Dict[str, Any]:
        """
        Get player rank information using the API client.

        Args:
            player_uuid: Player's UUID

        Returns:
            Dictionary with rank information
        """
        try:
            # Use the API client to get rank information
            if hasattr(self.api_client, 'get_player_rank'):
                rank_data = self.api_client.get_player_rank(player_uuid)
                if rank_data:
                    return {
                        'rank': rank_data.get('currenttierpatched', 'Unknown'),
                        'rr': rank_data.get('ranking_in_tier', 0),
                        'level': 0  # Level not available from rank endpoint
                    }
        except Exception as e:
            print(f"Error getting player rank for {player_uuid}: {e}")

        return {
            'rank': 'Unknown',
            'rr': 0,
            'level': 0
        }

    def _get_player_name_from_recent_matches(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Try to get player name from recent match history or other sources.

        Args:
            player_uuid: Player's UUID

        Returns:
            Player info dictionary or None
        """
        try:
            # This is a placeholder - in a real implementation, you might:
            # 1. Check recent match history for this UUID
            # 2. Use a local cache of UUID to name mappings
            # 3. Try other Valorant API endpoints

            # For now, return None as we don't have a reliable way to get names from UUIDs
            return None
        except Exception:
            return None

    def _extract_player_info(self, match_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract player information from match data and enrich with rank info.

        Args:
            match_data: Raw match data from local client

        Returns:
            List of player information dictionaries
        """
        players = []

        # Extract players from different possible structures
        player_list = []
        if 'AllyTeam' in match_data and 'EnemyTeam' in match_data:
            # Pregame structure
            player_list.extend(match_data.get('AllyTeam', {}).get('Players', []))
            player_list.extend(match_data.get('EnemyTeam', {}).get('Players', []))
        elif 'Players' in match_data:
            # Direct players list
            player_list = match_data.get('Players', [])

        if not player_list:
            return players

        # Extract all PUUIDs first
        puuids = []
        for player_data in player_list:
            player_uuid = player_data.get('Subject', '')
            if player_uuid:
                puuids.append(player_uuid)

        # Get all player names at once using the name service
        name_dict = {}
        if puuids:
            print(f"Getting names for {len(puuids)} players...")
            name_dict = self.client.get_player_names_from_puuids(puuids)
            print(f"Retrieved {len(name_dict)} player names")

        for player_data in player_list:
            try:
                player_uuid = player_data.get('Subject', '')
                character_id = player_data.get('CharacterID', '')
                team_id = player_data.get('TeamID', '')

                # Get player name from the name service results
                player_name = name_dict.get(player_uuid, 'Unknown')
                name_parts = player_name.split('#') if '#' in player_name else [player_name, 'Unknown']

                # Get rank information
                rank_info = self._get_player_rank(player_uuid)

                players.append({
                    'uuid': player_uuid,
                    'name': name_parts[0],
                    'tag': name_parts[1] if len(name_parts) > 1 else 'Unknown',
                    'rank': rank_info.get('rank', 'Unknown'),
                    'rr': rank_info.get('rr', 0),
                    'team': 'Red' if team_id == 'Red' else 'Blue',
                    'agent': self._get_agent_name(character_id),
                    'level': rank_info.get('level', 0)
                })

            except Exception as e:
                print(f"Error processing player data: {e}")
                continue

        return players

    def _parse_presence_data(self, presence_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse presence data to extract match information.

        Args:
            presence_data: Raw presence data from the client

        Returns:
            Match data dictionary or None
        """
        try:
            presences = presence_data.get('presences', [])

            # Find the current player's presence
            for presence in presences:
                if presence.get('product') == 'valorant':
                    private_data = presence.get('private')
                    if private_data:
                        # Decode base64 private data
                        try:
                            decoded_data = base64.b64decode(private_data).decode('utf-8')
                            private_json = json.loads(decoded_data)

                            # Extract match information
                            session_state = private_json.get('sessionLoopState')
                            queue_id = private_json.get('queueId')
                            match_map = private_json.get('partyOwnerMatchMap', private_json.get('matchMap'))
                            provisioning_flow = private_json.get('provisioningFlow')

                            print(f"Decoded presence data: sessionState={session_state}, queueId={queue_id}, map={match_map}")

                            # Check if in a match or agent select
                            if session_state == 'INGAME' or provisioning_flow == 'Matchmaking':
                                # Map the queue ID to a readable mode
                                mode_map = {
                                    'swiftplay': 'Swiftplay',
                                    'competitive': 'Competitive',
                                    'unrated': 'Unrated',
                                    'spikerush': 'Spike Rush',
                                    'deathmatch': 'Deathmatch',
                                    'escalation': 'Escalation',
                                    'replication': 'Replication'
                                }

                                # Extract map name from path
                                map_name = 'Unknown'
                                if match_map:
                                    map_parts = match_map.split('/')
                                    if len(map_parts) > 0:
                                        map_name = map_parts[-1]  # Get the last part (e.g., "Port" from "/Game/Maps/Port/Port")

                                mode_name = mode_map.get(queue_id, queue_id or 'Unknown')

                                # Determine state based on session_state, not provisioning_flow
                                # session_state is more accurate for determining actual game state
                                if session_state == 'INGAME':
                                    state = 'INGAME'
                                elif session_state == 'PREGAME':
                                    state = 'PREGAME'
                                else:
                                    # Fallback to provisioning_flow logic for other cases
                                    state = 'PREGAME' if provisioning_flow == 'Matchmaking' else 'INGAME'

                                return {
                                    'state': state,
                                    'map': map_name,
                                    'mode': mode_name,
                                    'queue_id': queue_id,
                                    'session_state': session_state,
                                    'players': []  # Will be populated later if needed
                                }

                        except (json.JSONDecodeError, binascii.Error) as e:
                            print(f"Error decoding presence private data: {e}")
                            continue

        except Exception as e:
            print(f"Error parsing presence data: {e}")

        return None

    def _get_player_info_by_uuid(self, player_uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get player information by UUID using the API.
        
        Args:
            player_uuid: Player's UUID
            
        Returns:
            Player information dictionary or None
        """
        try:
            # This would require a different API endpoint that accepts UUIDs
            # For now, return None as the HenrikDev API primarily uses name#tag
            return None
        except Exception:
            return None
    
    def _get_agent_name(self, character_id: str) -> str:
        """
        Convert character ID to agent name.
        
        Args:
            character_id: Character UUID
            
        Returns:
            Agent name
        """
        # Agent ID to name mapping (partial list)
        agent_map = {
            '5f8d3a7f-467b-97f3-062c-13acf203c006': 'Breach',
            'f94c3b30-42be-e959-889c-5aa313dba261': 'Raze',
            '22697a3d-45bf-8dd7-4fec-84a9e28c69d7': 'Chamber',
            '601dbbe7-43ce-be57-2a40-4abd24953621': 'KAY/O',
            '6f2a04ca-43e0-be17-7f36-b3908627744d': 'Skye',
            '117ed9e3-49f3-6512-3ccf-0cada7e3823b': 'Cypher',
            '320b2a48-4d9b-a075-30f1-1f93a9b638fa': 'Sova',
            '1e58de9c-4950-5125-93e9-a0aee9f98746': 'Killjoy',
            '95b78ed7-4637-86d9-7e41-71ba8c293152': 'Harbor',
            '8e253930-4c05-31dd-1b6c-************': 'Omen',
            '41fb69c1-4189-7b37-f117-bcaf1e96f1bf': 'Astra',
            '9f0d8ba9-4140-b941-57d3-a7ad57c6b417': 'Brimstone',
            'bb2a4828-46eb-8cd1-e765-15848195d751': 'Neon',
            '7f94d92c-4234-0a36-9646-3a87eb8b5c89': 'Yoru',
            'eb93336a-449b-9c1b-0a54-a891f7921d69': 'Phoenix',
            '569fdd95-4d10-43ab-ca70-79becc718b46': 'Sage',
            'a3bfb853-43b2-7238-a4f1-ad90e9e46bcc': 'Reyna',
            'dade69b4-4f5a-8528-247b-219e5a1facd6': 'Fade',
            'e370fa57-4757-3604-3648-499e1f642d3f': 'Gekko',
            '707eab51-4836-f488-046a-cda6bf494859': 'Viper',
            'add6443a-41bd-e414-f6ad-e58d267f4e95': 'Jett',
        }
        
        return agent_map.get(character_id, 'Unknown')
    
    def get_current_match_data(self) -> Optional[Dict[str, Any]]:
        """Get the current match data if available."""
        return self.current_match_data
    
    def is_in_match(self) -> bool:
        """Check if currently in a match."""
        return (self.current_match_data is not None and
                self.current_match_data.get('state') in ['PREGAME', 'INGAME', 'CONNECTED', 'DEATHMATCH'])
