"""
Account configuration management for Valorant tools.
Handles storing and retrieving the current account information.
"""

import os
import json
from typing import Optional, Dict, Tuple
from pathlib import Path

class AccountConfig:
    """Manages account configuration for Valorant tools."""
    
    def __init__(self):
        self.config_file = Path('account_config.json')
        self._config = self._load_config()
    
    def _load_config(self) -> Dict:
        """Load configuration from file."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading account config: {e}")
        
        return {}
    
    def _save_config(self):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self._config, f, indent=2)
        except Exception as e:
            print(f"Error saving account config: {e}")
    
    def set_account(self, name: str, tag: str):
        """
        Set the current account.
        
        Args:
            name: Riot ID name
            tag: Riot ID tag
        """
        self._config['current_account'] = {
            'name': name,
            'tag': tag
        }
        self._save_config()
        print(f"Account set to: {name}#{tag}")
    
    def get_account(self) -> Optional[Tuple[str, str]]:
        """
        Get the current account.
        
        Returns:
            Tuple of (name, tag) or None if not set
        """
        account = self._config.get('current_account')
        if account and 'name' in account and 'tag' in account:
            return account['name'], account['tag']
        return None
    
    def get_account_dict(self) -> Optional[Dict[str, str]]:
        """
        Get the current account as a dictionary.
        
        Returns:
            Dictionary with 'name' and 'tag' or None if not set
        """
        account = self.get_account()
        if account:
            return {
                'name': account[0],
                'tag': account[1]
            }
        return None
    
    def clear_account(self):
        """Clear the current account."""
        if 'current_account' in self._config:
            del self._config['current_account']
            self._save_config()
            print("Account cleared")
    
    def is_configured(self) -> bool:
        """Check if an account is configured."""
        return self.get_account() is not None

# Global instance
account_config = AccountConfig()
