# 🎯 Valorant Live Detection Terminal

A clean, terminal-based live detection tool for real-time Valorant match monitoring and teammate analysis.

## ✨ Features

- **🔄 Real-time monitoring** - Continuously monitors your Valorant matches
- **🎮 Game mode detection** - Detects Competitive, Unrated, Swiftplay, Deathmatch, etc.
- **🗺️ Map identification** - Shows current map name
- **👥 Player analysis** - Displays teammate ranks and information (when available)
- **🏆 Your rank display** - Shows your current rank and RR
- **📱 Clean interface** - Minimal, easy-to-read terminal display
- **⚡ Lightweight** - No web browser required, runs in terminal

## 🚀 Quick Start

### Method 1: Double-click launcher (Easiest)
1. **Start Valorant** first
2. **Double-click** `start_live_detection.bat` 
3. A new terminal window will open with live detection

### Method 2: Command line
```bash
# Make sure Valorant is running first
python live_detection_terminal.py
```

### Method 3: PowerShell
```powershell
# Run the PowerShell launcher
.\start_live_detection.ps1
```

## 📊 What You'll See

```
================================================================================
🎯 VALORANT LIVE DETECTION TERMINAL
================================================================================
⏰ Last Update: 15:30:45
🔄 Status: 🟢 MONITORING
--------------------------------------------------------------------------------
⚡ GAME STATE: SWIFTPLAY
🎮 MODE: Swiftplay
🗺️  MAP: Port
💬 INFO: Swiftplay detected - Limited player data available
--------------------------------------------------------------------------------
👥 PLAYERS (1):
#   NAME                 RANK                      TEAM         AGENT       
--------------------------------------------------------------------------------
1   You (rileyx)#umi     Immortal 1 (20 RR)       Your Team    Unknown     
--------------------------------------------------------------------------------
📝 NOTE: For full teammate analysis, try Competitive or Unrated matches
--------------------------------------------------------------------------------
```

## 🎮 Game Mode Support

| Mode | Detection | Player Data | Teammate Ranks |
|------|-----------|-------------|----------------|
| **Competitive** | ✅ Full | ✅ Complete | ✅ All teammates |
| **Unrated** | ✅ Full | ✅ Complete | ✅ All teammates |
| **Swiftplay** | ✅ Full | ⚠️ Limited | ❌ API restriction |
| **Deathmatch** | ✅ Full | ❌ None | ❌ No teams |
| **Practice Range** | ✅ Basic | ❌ None | ❌ Solo mode |

## 🔧 Requirements

- **Valorant running** - The game must be open and logged in
- **Python 3.7+** - Make sure Python is installed
- **HenrikDev API key** - Already configured in your `.env` file

## 💡 Usage Tips

### For Best Results:
1. **Queue Competitive or Unrated** for full teammate data
2. **Wait for agent select** - Best time for player detection
3. **Keep terminal open** - Monitors continuously until you close it

### Troubleshooting:
- **"Valorant not running"** → Start Valorant first
- **"Connection failed"** → Restart Valorant and try again
- **"No player data"** → Normal for Swiftplay/Deathmatch modes
- **Stuck on "Searching"** → Try entering a match or Practice Range

## ⌨️ Controls

- **Ctrl+C** - Stop monitoring and exit
- **Close terminal** - Stops the application
- **Restart Valorant** - Automatically reconnects when detected

## 🆚 Terminal vs Web Interface

| Feature | Terminal | Web Interface |
|---------|----------|---------------|
| **Performance** | ⚡ Faster | 🐌 Slower |
| **Resource usage** | 🪶 Lightweight | 🔋 Heavy |
| **Setup** | 🎯 Simple | 🔧 Complex |
| **Accessibility** | 📱 Any terminal | 🌐 Browser only |
| **Real-time updates** | ✅ Instant | ⏱️ Delayed |

## 🎯 Perfect For:

- **Streamers** - Clean overlay-friendly display
- **Competitive players** - Quick teammate rank checking
- **Casual monitoring** - Background match tracking
- **Low-resource setups** - Minimal system impact

## 🔄 Auto-restart

The terminal application will:
- ✅ **Auto-reconnect** if Valorant restarts
- ✅ **Handle disconnections** gracefully  
- ✅ **Update in real-time** as game state changes
- ✅ **Show clear status** at all times

---

**🎉 Enjoy your streamlined Valorant live detection experience!**
