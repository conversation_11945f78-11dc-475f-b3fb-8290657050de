#!/usr/bin/env python3
"""
Account setup for Valorant Live Detection.
Run this once to configure which account to use for rank detection.
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.account_config import account_config
from src.api_client import ValorantAPI

def setup_account():
    """Setup the account configuration."""
    print("🎯 Valorant Account Setup")
    print("=" * 50)
    print("This will configure which account to use for live detection.")
    print()
    
    # Check current configuration
    current_account = account_config.get_account()
    if current_account:
        name, tag = current_account
        print(f"📋 Current account: {name}#{tag}")
        
        change = input("Do you want to change it? (y/n): ").lower().strip()
        if change != 'y':
            print("✅ Keeping current account configuration.")
            return
        print()
    
    # Get new account info
    print("🔍 Enter your Valorant Riot ID:")
    print("(This is the name and tag you use to log into Valorant)")
    print()
    
    while True:
        name = input("Enter your Riot ID name: ").strip()
        if not name:
            print("❌ Name cannot be empty!")
            continue
        
        tag = input("Enter your Riot ID tag (without #): ").strip()
        if not tag:
            print("❌ Tag cannot be empty!")
            continue
        
        print(f"\n📋 You entered: {name}#{tag}")
        confirm = input("Is this correct? (y/n): ").lower().strip()
        
        if confirm == 'y':
            break
        else:
            print("Let's try again...\n")
    
    # Test the account
    print(f"\n🧪 Testing account: {name}#{tag}")
    try:
        api = ValorantAPI()
        player_info = api.get_player_full_info(name, tag)
        
        if player_info:
            print("✅ Account found!")
            print(f"   Level: {player_info.account_level}")
            print(f"   Rank: {player_info.display_rank}")
            print(f"   Peak: {player_info.display_peak}")
            
            # Save the configuration
            account_config.set_account(name, tag)
            print(f"\n🎉 Account configuration saved!")
            print(f"   The live detection will now use: {name}#{tag}")
            
        else:
            print("❌ Account not found!")
            print("Please check your Riot ID and try again.")
            return False
            
    except Exception as e:
        print(f"❌ Error testing account: {e}")
        print("Please check your Riot ID and try again.")
        return False
    
    return True

def main():
    """Main function."""
    try:
        if setup_account():
            print("\n" + "=" * 50)
            print("✅ Setup complete!")
            print("\n💡 You can now run the live detection:")
            print("   python live_detection_terminal.py")
            print("\n🔧 To change your account later:")
            print("   python setup_account.py")
        else:
            print("\n❌ Setup failed. Please try again.")
            
    except KeyboardInterrupt:
        print("\n\n🛑 Setup cancelled.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
