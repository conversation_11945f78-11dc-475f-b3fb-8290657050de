#!/usr/bin/env python3
"""
Simple test for Valorant live detection issues.
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all imports work."""
    print("🔧 Testing imports...")
    
    try:
        from src.valorant_client import ValorantLocalClient, is_valorant_running
        print("✅ valorant_client imported successfully")
    except Exception as e:
        print(f"❌ valorant_client import failed: {e}")
        return False
    
    try:
        from src.api_client import ValorantAPI
        print("✅ api_client imported successfully")
    except Exception as e:
        print(f"❌ api_client import failed: {e}")
        return False
    
    try:
        from src.live_match_detector import LiveMatchDetector
        print("✅ live_match_detector imported successfully")
    except Exception as e:
        print(f"❌ live_match_detector import failed: {e}")
        return False
    
    return True

def test_valorant_running():
    """Test if Valorant is running."""
    print("\n🎮 Testing if Valorant is running...")
    
    try:
        from src.valorant_client import is_valorant_running
        
        if is_valorant_running():
            print("✅ Valorant is running!")
            return True
        else:
            print("❌ Valorant is not running!")
            print("\n💡 To test live detection:")
            print("   1. Start Valorant")
            print("   2. Wait for it to fully load")
            print("   3. Run this script again")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Valorant: {e}")
        return False

def test_basic_connection():
    """Test basic connection to Valorant client."""
    print("\n🔌 Testing basic connection...")
    
    try:
        from src.valorant_client import ValorantLocalClient
        
        client = ValorantLocalClient()
        print("✅ ValorantLocalClient created")
        
        # Try to connect
        print("🔄 Attempting connection...")
        if client.connect():
            print("✅ Successfully connected to Valorant!")
            
            # Test basic functionality
            player_uuid = client._get_player_uuid()
            if player_uuid:
                print(f"✅ Got player UUID: {player_uuid[:8]}...")
            else:
                print("⚠️ Could not get player UUID")
            
            game_state = client.get_current_game_state()
            print(f"🎯 Game state: {game_state}")
            
            return True
        else:
            print("❌ Failed to connect to Valorant")
            print("\n🔧 Try these steps:")
            print("   1. Restart Valorant")
            print("   2. Run as Administrator")
            print("   3. Enter Practice Range")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def main():
    """Main test function."""
    print("🎯 Simple Valorant Live Detection Test")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_imports():
        print("\n❌ Import test failed - cannot proceed")
        return
    
    # Test 2: Valorant running
    if not test_valorant_running():
        print("\n⚠️ Valorant not running - live detection won't work")
        return
    
    # Test 3: Basic connection
    if not test_basic_connection():
        print("\n❌ Connection test failed")
        return
    
    print("\n✅ All basic tests passed!")
    print("\n💡 Your live detection system should work.")
    print("   If it's still not working, the issue might be:")
    print("   1. You're not in a supported game mode")
    print("   2. The match doesn't have accessible player data")
    print("   3. You need to be in Competitive/Unrated for full data")

if __name__ == "__main__":
    main()
